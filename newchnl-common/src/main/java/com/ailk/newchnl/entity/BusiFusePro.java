/**
 * $Id: busiFusePro.java,v 1.0 2023/12/29 14:39 asus Exp $
 * <p>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $Id: busiFusePro.java,v 1.1 2023/12/29 14:39 asus Exp $
 * Created on 2023/12/29 14:39
 */
public class BusiFusePro implements Serializable {
    private String billMonth;           //月份
    private Long channelEntityId;       //网点Id
    private String channelEntityName;   //网点名称
    private String orgName;             //属地分公司
    private Date doneDate;              //录入时间
    private Long opId;                  //操作员Id
    private Long orgId;                 //操作员属地Id
    private Integer recStatus;          //状态

    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public Long getChannelEntityId() {
        return channelEntityId;
    }

    public void setChannelEntityId(Long channelEntityId) {
        this.channelEntityId = channelEntityId;
    }

    public String getChannelEntityName() {
        return channelEntityName;
    }

    public void setChannelEntityName(String channelEntityName) {
        this.channelEntityName = channelEntityName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(Date doneDate) {
        this.doneDate = doneDate;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Integer getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Integer recStatus) {
        this.recStatus = recStatus;
    }
}
