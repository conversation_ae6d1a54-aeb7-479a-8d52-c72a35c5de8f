package com.ailk.newchnl.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 中高端保有项目人员月度考评实体类
 */
public class MidHighEndRetentionAssessment implements Serializable {
    private Long doneCode;                //序列号
    private String billMonth;               //录入月份
    private String roleName;                //角色名称，默认为中高端保有
    private String ebcJobNumber;            //EBC工号名称
    private Double assessmentCoefficient;   //当月考核系数【0.80-1.20】，保留两位小数
    private Double basicCostAdjustFee;      //基本费用调整费
    private Double adjustFee;               //调整费
    private String orgId;                   //归属组织ID
    private String orgName;                 //所属分公司
    private String opId;                    //操作员Id
    private String userName;                //操作员工号
    private Date doneDate;                  //操作时间
    private String recStatus;               //状态 0：待审核  1：审核通过  2：审核驳回   3：-1  失效
    private String recStatusList;           // 用来存放查询条件的字段，数据库中没有
    private String agentAdjustUseId;        //审批人Id
    private String recStatusApprove;        //审批状态
    private String fileName;                //附件名称
    private String filePath;                //附件下载路径


    private String billDate;                //操作时间 字符

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }

    public Long getDoneCode() {
        return doneCode;
    }

    public void setDoneCode(Long doneCode) {
        this.doneCode = doneCode;
    }

    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getEbcJobNumber() {
        return ebcJobNumber;
    }

    public void setEbcJobNumber(String ebcJobNumber) {
        this.ebcJobNumber = ebcJobNumber;
    }

    public Double getAssessmentCoefficient() {
        return assessmentCoefficient;
    }

    public void setAssessmentCoefficient(Double assessmentCoefficient) {
        this.assessmentCoefficient = assessmentCoefficient;
    }

    public Double getBasicCostAdjustFee() {
        return basicCostAdjustFee;
    }

    public void setBasicCostAdjustFee(Double basicCostAdjustFee) {
        this.basicCostAdjustFee = basicCostAdjustFee;
    }

    public Double getAdjustFee() {
        return adjustFee;
    }

    public void setAdjustFee(Double adjustFee) {
        this.adjustFee = adjustFee;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOpId() {
        return opId;
    }

    public void setOpId(String opId) {
        this.opId = opId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(Date doneDate) {
        this.doneDate = doneDate;
    }

    public String getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(String recStatus) {
        this.recStatus = recStatus;
    }

    public String getRecStatusList() {
        return recStatusList;
    }

    public void setRecStatusList(String recStatusList) {
        this.recStatusList = recStatusList;
    }

    public String getAgentAdjustUseId() {
        return agentAdjustUseId;
    }

    public void setAgentAdjustUseId(String agentAdjustUseId) {
        this.agentAdjustUseId = agentAdjustUseId;
    }

    public String getRecStatusApprove() {
        return recStatusApprove;
    }

    public void setRecStatusApprove(String recStatusApprove) {
        this.recStatusApprove = recStatusApprove;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}