/**
 * $Id: BusiThirdSalesRoleUpd.java,v 1.0 2024/5/14 17:13 asus Exp $
 * <p>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $Id: BusiThirdSalesRoleUpd.java,v 1.1 2024/5/14 17:13 asus Exp $
 * Created on 2024/5/14 17:13
 */
public class BusiThirdSalesRoleUpd implements Serializable {
    private Long doneCode;              //序列号
    private String billMonth;           //录入月
    private Long nodeId;                //网点Id
    private String nodeName;            //网点名称
    private String roleName;            //调整后角色
    private Integer roleId;             //角色对应Id code_type = 10055
    private Long orgId;                 //组织Id
    private String orgName;             //归属组织
    private Long opId;                  //操作员Id
    private String userName;            //操作员
    private Date doneDate;              //操作时间
    private Long agentAdjustUseId;      //审批人员id
    private String agentAdjustUserName; //审批员名称

    /**
     * 审批状态  0：待属地市场部三级审批  1：属地市场部三级审批通过 -- > 待市场部职能审批   2：属地市场部三级审批驳回
     *          3：市场部职能审批通过 -- > 审核通过   4：市场部职能审批驳回
     */
    private Integer auditStatus;        //审批状态
    private String ApproveStatus;    //存储审批状态
    private Integer recStatus;              //状态  1：有效  0：失效
    private String fileName;            //上传附件名称
    private String filePath;            //上传附件路径
    private String billDate;            //操作时间

    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(Date doneDate) {
        this.doneDate = doneDate;
    }

    public Long getAgentAdjustUseId() {
        return agentAdjustUseId;
    }

    public void setAgentAdjustUseId(Long agentAdjustUseId) {
        this.agentAdjustUseId = agentAdjustUseId;
    }

    public String getAgentAdjustUserName() {
        return agentAdjustUserName;
    }

    public void setAgentAdjustUserName(String agentAdjustUserName) {
        this.agentAdjustUserName = agentAdjustUserName;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getApproveStatus() {
        return ApproveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        ApproveStatus = approveStatus;
    }

    public Long getDoneCode() {
        return doneCode;
    }

    public void setDoneCode(Long doneCode) {
        this.doneCode = doneCode;
    }

    public Integer getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Integer recStatus) {
        this.recStatus = recStatus;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }
}
