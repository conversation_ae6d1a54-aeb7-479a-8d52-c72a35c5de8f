package com.ailk.newchnl.service;

import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.report.AdjustFeeDtl;
import com.ailk.newchnl.entity.resource.ChnlResBusiGoods;
import com.ailk.newchnl.entity.resource.ResGoods;
import com.ailk.newchnl.entity.xt.BusiThirdXXAssess;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;

import java.util.List;
import java.util.Map;

/**
 * Created by admin on 2015/5/5.
 */
public interface AgentServiceFeeService {
    public Map<String,String> agentServiceFeeSave(Long channelEntityId,Long billMonth,Long countFee,Long totalFee,Long totalPoint,Long deductFee,String notes,SPrivData sPrivData)throws Exception;
    public Map<String,String> findAgentFee(Long channelEntityId,Long accId,Integer billMonth)throws Exception;
    public Map<String,String> feeDefaultFeeAdd(Long channelEntityId,Integer billMonth,Long accId,Long totalFee,String notes)throws Exception;
    public PageData<AdjustFeeDtl> feeDefaultFeeQuery(Integer billMonth,Long accId,PageParameter page)throws Exception;
    public PageData<AgentServiceFeeDtl> serviceFeeQuery(Long nodeId,Long billMonth,PageParameter page)throws Exception;
    public Map<String,String> agentServiceFeeUpdate(Long channelEntityId,Long billMonth,Long countFee,Long totalFee,Long totalPoint,Long deductFee,String notes,SPrivData sPrivData)throws Exception;
    public Map<String,String> serviceFeeDelete(Long nodeId,Long billMonth)throws Exception;

    public PageData<ChnlResBusiGoods> busiGoodsQuery(Long resType,Long resCode,Long orgId,PageParameter page)throws Exception;
    public Map<String,String> busiGoodsTransfer(Long doneCode,Long orgId)throws Exception;
    public Map<String,String> resGoodsAdd(Long resType,Long resCode,String goodsName,Long startIccId,Long amount,Long orgId,Long opId)throws Exception;
    public PageData<ResGoods> resGoodsQuery(Long resType,Long resCode,Long orgId,PageParameter page)throws Exception;
    public Map<String,String> resGoodsTransfer(Long resId,Long toOrgId)throws Exception;
    public Map<String,String> resGoodsModify(Long resId,Long resCode,String goodsName,Long startIccId,Long amount,Long opId)throws Exception;

    public void addThreeSupportFee(ThreeSupportFeeAdd threeSupportFeeAdd,SPrivData sPrivData)throws Exception;

    public PageData<ThreeSupportFeeAdd> ManageThreeSupportFee(String begUpLoadDate, String endUpLoadDate, PageParameter page);

    public void addOAOExamine(OAOExamineAdd oaoExamineAdd)throws Exception;

    public PageData<OAOExamineAdd> ManageOAOExamine(OAOExamineAdd oAOExamineAdd,PageParameter page);

    public void EditOAOExamine(OAOExamineAdd oAOExamineAdd) throws Exception;

    public void addAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception;

    public void addAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception;

    public void addAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws Exception;

    public PageData<AgentAssessFeeAppraise> queryAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise, PageParameter page);

    public PageData<AgentAssessFeeJobContract> queryAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract, PageParameter page);

    public void editAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception;

    public void editChannelFuseedRigidTarget(ChannelFuseedRigidTarget channelFuseedRigidTarget) throws Exception;

    public void editAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws Exception;

    public void editAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception;

    public PageData<AgentServiceFeeAppraise> queryAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise, PageParameter page);


    public void delAgentServiceFeeAppraise(AgentServiceFeeAppraise agentServiceFeeAppraise) throws Exception;

    public void delAgentAssessFeeAppraise(AgentAssessFeeAppraise agentAssessFeeAppraise) throws Exception;

    public void delAgentAssessFeeJobContract(AgentAssessFeeJobContract agentAssessFeeJobContract) throws  Exception;

    public void addLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception;
    public void addLeagueNodeClassifyInfoForBatch(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception;
    public void addChannelFuseItemInfoForBatch(ChannelFuseItem channelFuseItem) throws Exception;

    public List<List<Object>>  batchAddLeagueNodeClassifyInfo(List<List<Object>> lists, Long opId, Long orgId) throws Exception;

    public List<List<Object>>  channelFuseItemInfo(List<List<Object>> lists, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception;

    public List<List<Object>>  channelFuseedRigidTargetInfo(List<List<Object>> lists, SPrivData sPrivData) throws Exception;

    public void editLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception;

    public PageData<LeagueNodeClassifyInfo> queryLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo, PageParameter page);

    public PageData<ChannelFuseItem> queryChannelFuseItemInfo(ChannelFuseItem channelFuseItem, PageParameter page);

    public PageData<ChannelFuseedRigidTarget> queryChannelFuseedRigidTargetInfo(ChannelFuseedRigidTarget channelFuseedRigidTarget, PageParameter page);

    public void delLeagueNodeClassifyInfo(LeagueNodeClassifyInfo leagueNodeClassifyInfo) throws Exception;

    public void delChannelFuseItemInfo(ChannelFuseItem channelFuseItem) throws Exception;

    public void companyScoresSave(String channelEntityName, CompanyAssessmentScore companyAssessmentScore)throws Exception;

    public void companyScoresModify(String channelEntityName, CompanyAssessmentScore companyAssessmentScore)throws Exception;

    public Boolean checkScoreIsExist(String channelEntityName, CompanyAssessmentScore companyAssessmentScore) throws Exception;

    public PageData<CompanyAssessmentScore> queryAssessmentScore(CompanyAssessmentScore companyAssessmentScore,PageParameter page) throws Exception;

    public List<CompanyAssessmentScore> queryDetailScoreInfo(CompanyAssessmentScore companyAssessmentScore) throws Exception;

    public Boolean checkTietongScoreIsExist(String channelEntityName, TietongItemScore tietongItemScore) throws Exception;

    public void tietongScoresSave(String channelEntityName, TietongItemScore tietongItemScore) throws Exception;

    public PageData queryTietongScore(TietongItemScore tietongItemScore, PageParameter page) throws Exception;

    public List<TietongItemScore> queryTietongDetailScoreInfo(TietongItemScore tietongItemScore) throws Exception;

    public void tietongScoresModify(String channelEntityName, TietongItemScore tietongItemScore) throws Exception;

    public Boolean checkContentIsExist(String channelEntityName, CheckContentScore checkContentScore) throws Exception;

    public void checkContentScoreSave(String channelEntityName, CheckContentScore checkContentScore) throws Exception;

    public PageData queryCheckContentScore(CheckContentScore checkContentScore, PageParameter page) throws Exception;

    public List<CheckContentScore> queryCheckContentDetailScoreInfo(CheckContentScore checkContentScore) throws Exception;

    public void checkContentModify(String channelEntityName, CheckContentScore checkContentScore) throws Exception;

    public List<ChannelSysBaseType> queryCodeName(String channelEntityName) throws Exception;
    //将在线公司录入考核录入上传数据置为0
    public void resetRecStatus(String billMonth)throws Exception;
    //将铁通看管项目录入考核录入上传数据置为0
    public void resetRecStatus2(String billMonth)throws Exception;


    //    获得在线公司录入考核录入上传的文件
    public String getRemoteFilePath(String billMonth) throws Exception;

    //    获得铁通看管项目录入考核录入上传的文件
    public String getRemoteFilePath2(String billMonth) throws Exception;


    PageData<AgentMarketingSupportFee> queryMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee, PageParameter page);

    public void editAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee) throws Exception;

    public void addAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee) throws Exception;

    public void delAgentMarketingSupportFee(AgentMarketingSupportFee agentMarketingSupportFee) throws Exception;

    PageData<BusinessThirdSupportFee> queryBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee, PageParameter page);

    void addBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception;

    void delBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception;

    void editBusinessThirdSupportFee(BusinessThirdSupportFee businessThirdSupportFee) throws Exception;

    PageData<BusinessThirdProjectRatio> queryBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio, PageParameter page) throws Exception;

    public void editBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception;

    public void addBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception;

    public void delBusinessThirdProjectRatio(BusinessThirdProjectRatio businessThirdProjectRatio) throws Exception;

    PageData<BusinessThirdProjectReach> queryBusinessThirdProjectReach(BusinessThirdProjectReach businessThirdProjectReach, PageParameter page) throws Exception;

    public void editBusinessThirdProjectReach(BusinessThirdProjectReach businessThirdProjectReach) throws Exception;

    public void businessThirdAssessmentAdd(BusinessThirdAssessment businessThirdAssessment, SPrivData sPrivData) throws Exception;


    PageData<BusinessThirdPersonMarket> queryBusinessThirdPersonMarket(BusinessThirdPersonMarket businessThirdPersonMarket, PageParameter page) throws Exception;

    public void editBusinessThirdPersonMarket(BusinessThirdPersonMarket businessThirdPersonMarket) throws Exception;

    public PageData<BusiThirdXXAssess> busiThirdXXAssessInfoQuery(BusiThirdXXAssess busiThirdXXAssess, PageParameter page);

    public PageData<BusinessThirdAssessment> businessThirdAssessmentQuery(BusinessThirdAssessment businessThirdAssessment, PageParameter page);

    public void busiThirdXXAssessEdit(BusiThirdXXAssess busiThirdXXAssess, SPrivData sPrivData) throws Exception;

    public void businessThirdAssessmentEdit(BusinessThirdAssessment businessThirdAssessment,SPrivData sPrivData) throws Exception;

    public List<List<Object>>  busiThirdXXAssessInfo(List<List<Object>> lists, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception;

    public void addBusiThirdXXAssessInfoInfoForBatch(BusiThirdXXAssess busiThirdXXAssess) throws Exception;

    public List<List<Object>>  threeNodeNameFeeBatchAdd(List<List<Object>> lists, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception;

    public PageData<ThreeNodeNameFee> threeNodeNameFeeInfoQuery(ThreeNodeNameFee threeNodeNameFee, PageParameter page);

    public PageData<ThreeSupportFeeAdd> threeSupportFeeInfoQuery(ThreeSupportFeeAdd threeSupportFeeAdd, PageParameter page);

    public void threeNodeNameFeeEdit(ThreeNodeNameFee threeNodeNameFee) throws Exception;

    public void threeSupportFeeAddEdit(ThreeSupportFeeAdd threeSupportFeeAdd) throws Exception;

    public void checkCompanySettleAdd(CompanySpecSettlement companySpecSettlement) throws Exception;

    public void companySpecSettlementAdd(CompanySpecSettlement companySpecSettlement) throws Exception;

    public PageData<CompanySpecSettlement> companySpecSettlementQuery(CompanySpecSettlement companySpecSettlement, PageParameter page);

    public void companySpecSettlementEdit(CompanySpecSettlement companySpecSettlement) throws Exception;

    public PageData<BusiFusePro> queryBusiFuseProInfo(BusiFusePro busiFusePro, PageParameter page);

    public void delBusiFuseProInfo(BusiFusePro busiFusePro) throws Exception;

    public List<List<Object>> busiFuseProInfo(List<List<Object>> lists, SPrivData sPrivData) throws Exception;

    public List<List<Object>>  busiThirdSalesRoleUpdInfo(List<List<Object>> lists, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception;

    public void busiThirdSalesRoleUpdInfoInfoForBatch(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd) throws Exception;

    public PageData<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdInfoQuery(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd, PageParameter page);

    public void busiThirdSalesRoleUpdEdit(BusiThirdSalesRoleUpd busiThirdSalesRoleUpd, SPrivData sPrivData) throws Exception;

    PageData<ChannelNodeGainAssessManage> queryChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage, PageParameter page) throws Exception;

    public void addChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage) throws Exception;

    public void delChannelNodeGainAssessManage(ChannelNodeGainAssessManage channelNodeGainAssessManage) throws Exception;

    public List<List<Object>>  dependencyBusiThirdXXJSJLInfo(List<List<Object>> lists,String remoteFileName, String remotePath, SPrivData sPrivData,Long agentAdjustUseId) throws Exception;
    public void addDependencyBusiThirdXXJSJLInfoForBatch(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL) throws Exception;
    public PageData<DependencyBusiThirdXXJSJL> dependencyBusiThirdXXJSJLInfoQuery(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL, PageParameter page) throws Exception;
    public void dependencyBusiThirdXXJSJLEdit(DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL, SPrivData sPrivData) throws Exception;
    public void addBusiThirdSetDateInfo(BusiThirdSetDate busiThirdSetDate) throws Exception;
    public PageData<BusiThirdSetDate> busiThirdSetDateInfoQuery(BusiThirdSetDate busiThirdSetDate, PageParameter page) throws Exception;

    public void executeXXAssessDate()throws Exception;

    public List<List<Object>>  busiThirdPriceServiceRatioAdd(List<List<Object>> lists, SPrivData sPrivData) throws Exception;

    public PageData<BusiThirdPriceServiceRatio> busiThirdPriceServiceRatioInfoQuery(BusiThirdPriceServiceRatio busiThirdPriceServiceRatio, PageParameter page) throws Exception;


    public List<List<Object>>  busiThirdSpecialOfferInfoAdd(List<List<Object>> lists, SPrivData sPrivData) throws Exception;

    public PageData<BusiThirdSpecialOfferInfo> busiThirdSpecialOfferInfoQuery(BusiThirdSpecialOfferInfo busiThirdSpecialOfferInfo, PageParameter page) throws Exception;


    public void addTietongOnlineEval(TietongOnlineEval tietongOnlineEval) throws Exception;
	
    public PageData<TietongOnlineEval> queryTietongOnlineEval(TietongOnlineEval tietongOnlineEval, PageParameter page) throws Exception;
	
    public void editTietongOnlineEval(TietongOnlineEval tietongOnlineEval) throws Exception;

    public void addTieTongSupportFee(TieTongSupportFeeAdd tieTongSupportFeeAdd,SPrivData sPrivData)throws Exception;
    public PageData<TieTongSupportFeeAdd> ManageTieTongSupportFee(String begUpLoadDate, String endUpLoadDate, PageParameter page);

    public PageData<TieTongSupportFeeAdd> tieTongSupportFeeInfoQuery(TieTongSupportFeeAdd tieTongSupportFeeAdd, PageParameter page);

    public void tieTongSupportFee_AddOrEdit(TieTongSupportFeeAdd tieTongSupportFeeAdd, SPrivData sPrivData) throws Exception;

    public void busiThirdDependRoleAdd(BusiThirdDependRole busiThirdDependRole) throws Exception;

    public PageData<BusiThirdDependRole> busiThirdDependRoleManage(Long orgId, PageParameter page) throws Exception;

    public void busiThirdDependRoleEdit(BusiThirdDependRole busiThirdDependRole) throws Exception;

    public void sendMessageForDependRole() throws Exception;

    public void sendDependencyMessage() throws Exception;

    public List<List<Object>>  midHighEndRetentionAssessment(List<List<Object>> lists, Long agentAdjustUseId, String remoteFileName, String remotePath, SPrivData sPrivData) throws Exception;


}
